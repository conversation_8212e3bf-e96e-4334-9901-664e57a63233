import React from "react";
import { IoClose } from "react-icons/io5";
import { FaPlay, FaFileAlt, FaUser, FaCalendar, FaDollarSign, FaTag } from "react-icons/fa";
import "../../styles/StrategyDetailsModal.css";

const StrategyDetailsModal = ({ strategy, isOpen, onClose }) => {
  if (!isOpen || !strategy) return null;

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatPrice = (price) => {
    return typeof price === "number" ? `$${price.toFixed(2)}` : price;
  };

  const getStatusBadge = (isActive) => {
    return (
      <span className={`status-badge ${isActive === 1 ? "active" : "inactive"}`}>
        {isActive === 1 ? "Active" : "Inactive"}
      </span>
    );
  };

  const getContentTypeIcon = (contentType) => {
    switch (contentType?.toLowerCase()) {
      case "video":
        return <FaPlay className="content-icon" />;
      case "pdf":
      case "document":
        return <FaFileAlt className="content-icon" />;
      default:
        return <FaFileAlt className="content-icon" />;
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="strategy-details-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Strategy Details</h2>
          <button className="close-btn" onClick={onClose}>
            <IoClose />
          </button>
        </div>

        <div className="modal-content">
          <div className="strategy-header">
            <div className="strategy-thumbnail">
              {strategy.thumbnailUrl ? (
                <img src={strategy.thumbnailUrl} alt={strategy.title} />
              ) : (
                <div className="placeholder-thumbnail">
                  {getContentTypeIcon(strategy.contentType)}
                </div>
              )}
            </div>
            <div className="strategy-info">
              <h3 className="strategy-title">{strategy.title}</h3>
              <div className="strategy-meta">
                <div className="meta-item">
                  <FaUser className="meta-icon" />
                  <span>Coach: {strategy.coachName || "N/A"}</span>
                </div>
                <div className="meta-item">
                  <FaCalendar className="meta-icon" />
                  <span>Created: {formatDate(strategy.createdAt)}</span>
                </div>
                <div className="meta-item">
                  <FaDollarSign className="meta-icon" />
                  <span>Price: {formatPrice(strategy.price)}</span>
                </div>
                <div className="meta-item">
                  <FaTag className="meta-icon" />
                  <span>Category: {strategy.category}</span>
                </div>
              </div>
              <div className="strategy-status">
                {getStatusBadge(strategy.isActive)}
              </div>
            </div>
          </div>

          <div className="strategy-details">
            <div className="detail-section">
              <h4>Description</h4>
              <p>{strategy.description}</p>
            </div>

            <div className="detail-section">
              <h4>About Coach</h4>
              <p>{strategy.aboutCoach}</p>
            </div>

            <div className="detail-section">
              <h4>Strategic Content</h4>
              <p>{strategy.strategicContent}</p>
            </div>

            <div className="detail-grid">
              <div className="detail-item">
                <h5>Sport</h5>
                <p>{strategy.sport}</p>
              </div>
              <div className="detail-item">
                <h5>Content Type</h5>
                <p>{strategy.contentType}</p>
              </div>
              <div className="detail-item">
                <h5>Difficulty</h5>
                <p>{strategy.difficulty}</p>
              </div>
              <div className="detail-item">
                <h5>Language</h5>
                <p>{strategy.language}</p>
              </div>
              <div className="detail-item">
                <h5>Sale Type</h5>
                <p>{strategy.saleType}</p>
              </div>
              <div className="detail-item">
                <h5>Status</h5>
                <p>{strategy.status}</p>
              </div>
              <div className="detail-item">
                <h5>Visibility</h5>
                <p>{strategy.visibility}</p>
              </div>
              {strategy.duration && (
                <div className="detail-item">
                  <h5>Duration</h5>
                  <p>{strategy.duration} minutes</p>
                </div>
              )}
            </div>

            {strategy.tags && strategy.tags.length > 0 && (
              <div className="detail-section">
                <h4>Tags</h4>
                <div className="tags-container">
                  {strategy.tags.map((tag, index) => (
                    <span key={index} className="tag">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {strategy.prerequisites && strategy.prerequisites.length > 0 && (
              <div className="detail-section">
                <h4>Prerequisites</h4>
                <ul>
                  {strategy.prerequisites.map((prereq, index) => (
                    <li key={index}>{prereq}</li>
                  ))}
                </ul>
              </div>
            )}

            {strategy.learningObjectives && strategy.learningObjectives.length > 0 && (
              <div className="detail-section">
                <h4>Learning Objectives</h4>
                <ul>
                  {strategy.learningObjectives.map((objective, index) => (
                    <li key={index}>{objective}</li>
                  ))}
                </ul>
              </div>
            )}

            {strategy.equipment && strategy.equipment.length > 0 && (
              <div className="detail-section">
                <h4>Equipment</h4>
                <ul>
                  {strategy.equipment.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="detail-section">
              <h4>File Information</h4>
              <div className="file-info">
                <p><strong>File URL:</strong> {strategy.fileUrl}</p>
                {strategy.fileSize && (
                  <p><strong>File Size:</strong> {(strategy.fileSize / 1024 / 1024).toFixed(2)} MB</p>
                )}
                {strategy.videoLength && (
                  <p><strong>Video Length:</strong> {strategy.videoLength} minutes</p>
                )}
              </div>
            </div>

            <div className="detail-section">
              <h4>Timestamps</h4>
              <div className="timestamps">
                <p><strong>Created:</strong> {formatDate(strategy.createdAt)}</p>
                <p><strong>Last Updated:</strong> {formatDate(strategy.lastUpdated)}</p>
                {strategy.publishedDate && (
                  <p><strong>Published:</strong> {formatDate(strategy.publishedDate)}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default StrategyDetailsModal;
